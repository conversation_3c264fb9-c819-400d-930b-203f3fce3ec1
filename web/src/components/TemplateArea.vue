<template>
    <div class="template-area-container" :style="backgroundStyle">
        <div class="draggable-container" @click="handleCanvasClick($event)">
            <draggable-resizable v-for="(item, index) in materialList" :key="`${item.clientKey}-${index}`"
                :snapToGrid="true" :x="item.x_axis < 0 ? 0 : item.x_axis" :y="item.y_axis < 0 ? 0 : item.y_axis"
                :z="item.template_index"
                :w="item.template_sm_type == 2 ? 240 : item.width ? item.width : ((item.source_width || 300) / 3)"
                :h="item.template_sm_type == 2 ? 50 : item.height ? item.height : ((item.source_height || 200) / 3)"
                :min-width="getMinWidth(item)" :snap="true" :snapTolerance="10" :min-height="getMinHeight(item)"
                :parent-w="canvasWidth" :parent-h="canvasHeight" :dragging="isDraggable(item)"
                :resizing="isResizable(item)" :parent="true" @dragging="(left, top) => handleDragging(left, top, index)"
                @drag-start="() => handleDragStart(index)" @drag-end="() => handleDragEnd(index)"
                @resizing="(left, top, width, height) => handleResizing(left, top, width, height, index)"
                @resize-start="() => handleResizeStart(index)" @resize-end="() => handleResizeEnd(index)"
                @dragover.native.prevent="handleDragOver($event, index)" @drop.native="handleDrop($event, index)"
                @click.native="handleAreaClick(index)" :class="['draggable', {
                    'highlighted': index === highlightedIndex,
                    'drop-target': isDropTarget(index) === 'valid',
                    'drop-target invalid': isDropTarget(index) === 'invalid'
                }]" :style="getAreaStyle(item)" :title="showAreaInfo(item)">
                <div class="area-type-label" v-if="item.areaConfig && !item.path">
                    {{ item.areaConfig.name }}
                </div>

                <!-- 图片轮播区域 -->
                <div v-if="item.type == 1 && item.template_sm_type == 1 && item.multiFiles && item.multiFiles.length > 1"
                    class="carousel-container">
                    <div class="carousel-wrapper"
                        :style="{ transform: `translateX(-${(currentSlideIndex[item.clientKey] || 0) * 100}%)` }">
                        <div v-for="(file, fileIndex) in item.multiFiles" :key="`${file.clientKey}-${fileIndex}`"
                            class="carousel-slide">
                            <img style="width: 100%; height: 100%" :src="imageUrl + file.path" alt="图片" class="media" />
                        </div>
                    </div>
                    <!-- 调试信息 -->
                    <div
                        style="position: absolute; top: 5px; left: 5px; background: rgba(0,0,0,0.7); color: white; padding: 2px 5px; font-size: 10px; z-index: 1000;">
                        轮播: {{ item.clientKey }} | 索引: {{ currentSlideIndex[item.clientKey] || 0 }} | 文件数: {{
                            item.multiFiles.length }}
                    </div>
                    <!-- 轮播指示器 -->
                    <div class="carousel-indicators" v-if="item.multiFiles.length > 1">
                        <span v-for="(file, fileIndex) in item.multiFiles" :key="`${file.clientKey}-${fileIndex}`"
                            :class="['indicator', { active: (currentSlideIndex[item.clientKey] || 0) === fileIndex }]"
                            @click="setSlideIndex(item.clientKey, fileIndex)"></span>
                    </div>
                </div>
                <!-- 单个图片 -->
                <img v-else-if="item.type == 1 && item.template_sm_type == 1" style="width: 100%; height: 100%"
                    :src="getMediaPath(item)" alt="图片" class="media" />

                <!-- 单个视频 -->
                <video v-if="item.type == 2 && item.template_sm_type == 1" style="width: 100%; height: 100%"
                    :src="getMediaPath(item)" class="media"></video>

                <dateTime v-if="item.template_sm_type == 2"></dateTime>

                <div v-if="item.template_sm_type == 5" style="width: 100%; height: 100%; position: relative;">
                    <div style="position: absolute; width: 100%; height: 100% ;top: 0px; left: 0px"></div>
                    <iframe :src="item.url" width="100%" height="100%" frameborder="0"
                        sandbox="allow-same-origin;allow-scripts;allow-forms"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
                </div>

                <!-- 文本跑马灯区域 -->
                <div v-if="item.template_sm_type == 6" class="text-marquee-container"
                    :style="getTextMarqueeStyle(item)">
                    <div class="text-marquee-content" :style="getTextMarqueeContentStyle(item)">
                        {{ item.textContent || $t('template.propertiesPanel.textContentPlaceholder') }}
                    </div>
                </div>
                <img class="del" @click="handleDeleteMaterial(index)" :src="require('@/assets/delete.png')" alt="" />
            </draggable-resizable>
        </div>
    </div>
</template>

<script>
import DraggableResizable from "vue-draggable-resizable";
import dateTime from "@/components/dateTime.vue";
import "vue-draggable-resizable/dist/VueDraggableResizable.css";

export default {
    name: 'TemplateArea',
    components: { DraggableResizable, dateTime },
    props: {
        materialList: {
            type: Array,
            required: true
        },
        highlightedIndex: {
            type: Number,
            default: -1
        },
        imageUrl: {
            type: String,
            required: true
        },
        areaTypeConfigs: {
            type: Object,
            required: true
        },
        canvasWidth: {
            type: Number,
            default: 960
        },
        canvasHeight: {
            type: Number,
            default: 540
        },
        backgroundUrl: {
            type: String,
            default: ''
        },
        backgroundSettings: {
            type: Object,
            default: () => ({
                backgroundSize: 'cover',
                backgroundPosition: 'center center'
            })
        }
    },
    data () {
        return {
            currentSlideIndex: {}, // 跟踪每个轮播区域的当前幻灯片索引
            carouselTimers: {}, // 存储每个轮播区域的定时器
        };
    },
    computed: {
        backgroundStyle () {
            const style = {
                width: `${this.canvasWidth}px`,
                height: `${this.canvasHeight}px`,
                position: 'relative'
            };

            if (this.backgroundUrl) {
                style.backgroundImage = `url(${this.imageUrl + this.backgroundUrl})`;
                style.backgroundSize = (this.backgroundSettings.backgroundSize === 'repeat')
                    ? 'auto'
                    : (this.backgroundSettings.backgroundSize || 'cover');
                style.backgroundRepeat = (this.backgroundSettings.backgroundSize === 'repeat')
                    ? 'repeat'
                    : 'no-repeat';
                style.backgroundPosition = this.backgroundSettings.backgroundPosition || 'center center';
            } else {
                style.backgroundColor = '#e2e2e2';
            }

            return style;
        }
    },

    mounted () {
        // 组件挂载后启动所有轮播
        this.$nextTick(() => {
            this.materialList.forEach(item => {
                if (item.multiFiles && item.multiFiles.length > 1) {
                    this.startIndividualCarousel(item);
                }
            });
        });
    },

    beforeDestroy () {
        // 清除所有轮播定时器
        if (this.carouselTimers) {
            Object.keys(this.carouselTimers).forEach(clientKey => {
                if (this.carouselTimers[clientKey]) {
                    clearInterval(this.carouselTimers[clientKey]);
                }
            });
        }
    },

    methods: {


        // 拖拽相关方法 - 优化性能
        handleDragging (left, top, index) {
            this.$emit('dragging', left, top, index);
        },

        handleDragStart (index) {
            // 拖拽开始时暂停轮播，避免冲突
            this.pauseAutoCarousel();
            this.$emit('drag-start', index);
        },

        handleDragEnd (index) {
            // 拖拽结束后恢复轮播
            this.resumeAutoCarousel();
            this.$emit('drag-end', index);
        },

        handleResizing (left, top, width, height, index) {
            this.$emit('resizing', left, top, width, height, index);
        },

        handleResizeStart (index) {
            // 拉伸开始时暂停轮播，避免冲突
            this.pauseAutoCarousel();
            this.$emit('resize-start', index);
        },

        handleResizeEnd (index) {
            // 拉伸结束后恢复轮播
            this.resumeAutoCarousel();
            this.$emit('resize-end', index);
        },

        handleDragOver (event, index) {
            this.$emit('dragover', event, index);
        },

        handleDrop (event, index) {
            this.$emit('drop', event, index);
        },

        handleDeleteMaterial (index) {
            this.$emit('delete-material', index);
        },

        // 处理区域点击事件
        handleAreaClick (index) {
            // 设置当前高亮区域
            this.$emit('area-click', index);
        },

        handleCanvasClick (event) {
            if (event.target === event.currentTarget) {
                this.$emit('canvas-click');
            }
        },

        // 重置轮播索引
        resetCarouselIndex (clientKey) {
            this.$set(this.currentSlideIndex, clientKey, 0);
        },

        // 更新轮播时间间隔
        updateCarouselInterval (clientKey) {
            // 找到对应的素材
            const item = this.materialList.find(item => item.clientKey === clientKey);
            if (item && item.multiFiles && item.multiFiles.length > 1) {
                // 重新启动该轮播区域的定时器
                this.startIndividualCarousel(item);
            }
        },

        // 工具方法
        getMinWidth (item) {
            if (item.template_sm_type == 2) {
                return 140;
            }
            if (item.areaConfig) {
                return item.areaConfig.minWidth;
            }
            return 50;
        },

        getMinHeight (item) {
            if (item.template_sm_type == 2) {
                return 50;
            }
            if (item.areaConfig) {
                return item.areaConfig.minHeight;
            }
            return 50;
        },

        isDraggable (item) {
            if (item.areaConfig) {
                return item.areaConfig.draggable;
            }
            return true;
        },

        isResizable (item) {
            if (item.areaConfig) {
                return item.areaConfig.resizable;
            }
            return true;
        },

        getAreaStyle (item) {
            if (item.areaConfig) {
                return {
                    backgroundColor: item.areaConfig.backgroundColor,
                    border: item.areaConfig.border
                };
            }
            return {};
        },

        showAreaInfo (item) {
            const config = item.areaConfig || {};
            return `${config.name || item.sm_name} - ${item.width}x${item.height}px`;
        },

        isDropTarget (index) {
            this.$emit('is-drop-target', index);
        },

        // 轮播相关方法
        setSlideIndex (clientKey, index) {
            this.$set(this.currentSlideIndex, clientKey, index);
        },

        startIndividualCarousel (item) {
            const clientKey = item.clientKey;
            if (this.carouselTimers[clientKey]) {
                clearInterval(this.carouselTimers[clientKey]);
            }

            this.carouselTimers[clientKey] = setInterval(() => {
                const currentIndex = this.currentSlideIndex[clientKey] || 0;
                const nextIndex = (currentIndex + 1) % item.multiFiles.length;
                this.$set(this.currentSlideIndex, clientKey, nextIndex);
            }, (item.multiFiles[0]?.interval_time || 5) * 1000);
        },

        pauseAutoCarousel () {
            // 暂停所有轮播
            Object.keys(this.carouselTimers).forEach(clientKey => {
                if (this.carouselTimers[clientKey]) {
                    clearInterval(this.carouselTimers[clientKey]);
                }
            });
        },

        resumeAutoCarousel () {
            // 恢复所有轮播
            this.materialList.forEach(item => {
                if (item.multiFiles && item.multiFiles.length > 1) {
                    this.startIndividualCarousel(item);
                }
            });
        },
        getMediaPath (item) {
            if (item.path) {
                return this.imageUrl + item.path;
            }
            if (item.multiFiles && item.multiFiles.length > 0 && item.multiFiles[0].path) {
                return this.imageUrl + item.multiFiles[0].path;
            }
            return ''; // 返回一个空字符串或一个占位符图片的URL
        },

        // 获取文本跑马灯容器样式
        getTextMarqueeStyle (item) {
            return {
                width: '100%',
                height: '100%',
                position: 'relative',
                overflow: 'hidden',
                backgroundColor: item.backgroundColor || '#ffffff'
            };
        },

        // 获取文本跑马灯内容样式
        getTextMarqueeContentStyle (item) {
            const baseStyle = {
                fontSize: `${item.fontSize || 24}px`,
                color: item.fontColor || '#000000',
                whiteSpace: 'nowrap',
                position: 'absolute',
                display: 'inline-block'
            };

            // 根据滚动方向设置初始位置和动画
            switch (item.scrollDirection) {
                case 'left':
                    baseStyle.left = '100%';
                    baseStyle.top = '50%';
                    baseStyle.transform = 'translateY(-50%)';
                    baseStyle.animation = `scrollLeft ${item.scrollSpeed || 50}s linear infinite`;
                    break;
                case 'right':
                    baseStyle.right = '100%';
                    baseStyle.top = '50%';
                    baseStyle.transform = 'translateY(-50%)';
                    baseStyle.animation = `scrollRight ${item.scrollSpeed || 50}s linear infinite`;
                    break;
                case 'up':
                    baseStyle.left = '50%';
                    baseStyle.bottom = '100%';
                    baseStyle.transform = 'translateX(-50%)';
                    baseStyle.animation = `scrollUp ${item.scrollSpeed || 50}s linear infinite`;
                    break;
                case 'down':
                    baseStyle.left = '50%';
                    baseStyle.top = '100%';
                    baseStyle.transform = 'translateX(-50%)';
                    baseStyle.animation = `scrollDown ${item.scrollSpeed || 50}s linear infinite`;
                    break;
                default:
                    baseStyle.left = '100%';
                    baseStyle.top = '50%';
                    baseStyle.transform = 'translateY(-50%)';
                    baseStyle.animation = `scrollLeft ${item.scrollSpeed || 50}s linear infinite`;
            }

            return baseStyle;
        },
    }
}
</script>

<style scoped lang="scss">
.template-area-container {
    /* The size is now controlled by the inline 'style' attribute */
}

.draggable-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.draggable {
    cursor: move;
    /* 移除transition以提高拖拽性能 */
}

.draggable:hover {
    outline: 1px solid #409EFF;
    box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.draggable:active {
    transform: scale(0.98);
    /* 移除transition以提高拖拽性能 */
}

.drop-target {
    outline: 3px dashed #67c23a !important;
    box-shadow: 0 0 20px rgba(103, 194, 58, 0.7) !important;
    transform: scale(1.05) !important;
    background-color: rgba(103, 194, 58, 0.1) !important;
}

.drop-target.invalid {
    outline: 3px dashed #f56c6c !important;
    box-shadow: 0 0 20px rgba(245, 108, 108, 0.7) !important;
    background-color: rgba(245, 108, 108, 0.1) !important;
}

.draggable.highlighted {
    outline: 2px solid #409EFF;
    box-shadow: 0 0 15px rgba(64, 158, 255, 0.6);
    transform: scale(1.02);
    // transition: all 0.2s ease;
    z-index: 1000;
}

.area-type-label {
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    border-bottom-right-radius: 6px;
    z-index: 10;
}

.del {
    width: 24px;
    height: 24px;
    position: absolute;
    right: 4px;
    top: 4px;
    cursor: pointer;
    display: none;
    border: 1px solid #f0f0f0;
    border-radius: 100px;
    background-color: #8383835c;
}

.draggable:hover .del {
    display: block;
}

// 轮播样式
.carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.carousel-wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    // transition: transform 0.15s ease-out; /* 减少过渡时间，提高性能 */
}

.carousel-slide {
    flex: 0 0 100%;
    width: 100%;
    height: 100%;
    position: relative;
}

.carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 10;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.2s ease;

    &.active {
        background-color: rgba(255, 255, 255, 0.9);
        transform: scale(1.2);
    }

    &:hover {
        background-color: rgba(255, 255, 255, 0.7);
    }
}

.carousel-prev,
.carousel-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    &:hover {
        background: rgba(0, 0, 0, 0.9);
        border-color: rgba(255, 255, 255, 0.6);
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }

    &:active {
        transform: translateY(-50%) scale(0.95);
    }

    i {
        font-size: 16px;
        font-weight: bold;
    }
}

.carousel-prev {
    left: 10px;
}

.carousel-next {
    right: 10px;
}

// 文本跑马灯样式
.text-marquee-container {
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.text-marquee-content {
    font-weight: 500;
    user-select: none;
}

// 跑马灯动画
@keyframes scrollLeft {
    0% {
        left: 100%;
    }

    100% {
        left: -100%;
    }
}

@keyframes scrollRight {
    0% {
        right: 100%;
    }

    100% {
        right: -100%;
    }
}

@keyframes scrollUp {
    0% {
        bottom: 100%;
    }

    100% {
        bottom: -100%;
    }
}

@keyframes scrollDown {
    0% {
        top: 100%;
    }

    100% {
        top: -100%;
    }
}
</style>
